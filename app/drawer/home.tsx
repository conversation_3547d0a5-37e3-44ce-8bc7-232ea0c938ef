import React, { useCallback, useState } from "react";
import {
  StyleSheet,
  Animated,
  Text,
  View,
  TouchableOpacity,
  TouchableWithoutFeedback,
  <PERSON><PERSON><PERSON><PERSON>,
  FlatList,
} from "react-native";
import Header from "@/components/header";
import Progress from "@/components/progress";
import FloatingMenu from "@/components/floatingMenu";
import { router, useFocusEffect } from "expo-router";
import PageLayout from "@/components/pageLayout";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { CrimsonLuxe } from "@/constants/Colors";
import TaskCard from "@/components/taskStatusCard";
import SleepLogCard from "@/components/sleepLogCard";
import { getChecklistCount } from "@/db/service/ChecklistService";
import { getEventCount } from "@/db/service/EventService";
import { getAllSleeplogs } from "@/db/service/SleeplogService";
import NoDataSVG from "@/assets/svgs/NoEvents.svg";

interface SleepLog {
  id: string;
  title: string;
  sleepTime: Date;
  wakeTime?: Date;
  isCompleted: boolean;
  notes?: string;
  duration?: number;
  quality?: number;
  autoQuality?: number;
  createdAt: Date;
}

const Home = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [checklistCount, setChecklistCount] = useState<number>(0);
  const [eventCount, setEventCount] = useState<number>(0);

  const [incompleteSleeplogs, setIncompleteSleeplogs] = useState<SleepLog[]>(
    [],
  );

  useFocusEffect(
    useCallback(() => {
      const fetchCounts = async () => {
        try {
          const [checklistRes, eventRes, allSleepRes] = await Promise.all([
            getChecklistCount(),
            getEventCount(),
            getAllSleeplogs(),
          ]);

          setChecklistCount(checklistRes.total || 0);
          setEventCount(eventRes.total || 0);

          // Filter only incomplete sleep logs (created but not woken up yet)
          const incomplete = allSleepRes.filter((log) => !log.isCompleted);
          setIncompleteSleeplogs(incomplete);
        } catch (error) {
          console.error("Failed to fetch counts:", error);
        }
      };

      fetchCounts();
    }, []),
  );

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        BackHandler.exitApp();
        return true;
      };

      const subscription = BackHandler.addEventListener("hardwareBackPress", onBackPress);

      return () => subscription.remove();
    }, []),
  );

  const hasData =
    eventCount > 0 || checklistCount > 0 || incompleteSleeplogs.length > 0;

  // Create data array for FlatList
  const homeData = [];

  // Add header
  homeData.push({ type: "header", id: "header" });

  if (hasData) {
    // Add progress
    homeData.push({ type: "progress", id: "progress" });

    // Add today's tasks if events exist
    if (eventCount > 0) {
      homeData.push({ type: "todayTasks", id: "todayTasks" });
    }

    // Add sleep logs section - only show when there are incomplete sleep logs
    if (incompleteSleeplogs.length > 0) {
      homeData.push({ type: "sleepLogsHeader", id: "sleepLogsHeader" });
      // Show incomplete sleep logs
      incompleteSleeplogs.forEach((sleeplog, index) => {
        homeData.push({
          type: "sleepLog",
          id: `sleeplog_${sleeplog.id}`,
          data: sleeplog,
        });
      });
    }
  } else {
    // Add no data state
    homeData.push({ type: "noData", id: "noData" });
  }

  const handleSleepLogPress = (sleepLog: SleepLog) => {
    router.push({
      pathname: "/sleepLogDetail",
      params: { id: sleepLog.id },
    });
  };

  const handleSleepLogUpdate = () => {
    // Refresh data when sleep log is updated (completed)
    const fetchCounts = async () => {
      try {
        const allSleepRes = await getAllSleeplogs();

        // Filter only incomplete sleep logs
        const incomplete = allSleepRes.filter((log) => !log.isCompleted);
        setIncompleteSleeplogs(incomplete);
      } catch (error) {
        console.error("Failed to refresh sleep logs:", error);
      }
    };
    fetchCounts();
  };

  const renderItem = ({ item }: { item: any }) => {
    switch (item.type) {
      case "header":
        return <Header />;

      case "progress":
        return <Progress />;

      case "todayTasks":
        return (
          <View style={styles.section}>
            <View style={styles.todayTaskWrapper}>
              <Text style={styles.sectionTitle}>Today's tasks</Text>
              <TouchableOpacity onPress={() => router.push("/drawer/calender")}>
                <MaterialCommunityIcons
                  name="calendar-month-outline"
                  size={28}
                  color={CrimsonLuxe.primary400}
                />
              </TouchableOpacity>
            </View>
            <TaskCard />
          </View>
        );

      case "sleepLogsHeader":
        return (
          <View style={styles.section}>
            <View style={styles.todayTaskWrapper}>
              <Text style={styles.sectionTitle}>
                {incompleteSleeplogs.length > 0
                  ? "Active Sleep Session"
                  : "Sleep Tracking"}
              </Text>
              <TouchableOpacity
                onPress={() => router.push("/drawer/sleepLogs")}
              >
                <MaterialCommunityIcons
                  name="sleep"
                  size={28}
                  color={CrimsonLuxe.primary400}
                />
              </TouchableOpacity>
            </View>
          </View>
        );

      case "sleepLog":
        return (
          <SleepLogCard
            sleeplog={item.data}
            onPress={() => handleSleepLogPress(item.data)}
            onUpdate={handleSleepLogUpdate}
            showQuality={false}
          />
        );

      case "noData":
        return (
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>No Data Available</Text>
            <NoDataSVG width={"90%"} height={300} />
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <PageLayout style={styles.container} scrollable={false}>
        <Animated.View style={[styles.content]}>
          <FlatList
            data={homeData}
            keyExtractor={(item) => item.id}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.flatListContent}
          />
        </Animated.View>
      </PageLayout>

      <View style={StyleSheet.absoluteFill} pointerEvents="box-none">
        {isMenuOpen && (
          <TouchableWithoutFeedback onPress={() => setIsMenuOpen(false)}>
            <View style={styles.backdrop} />
          </TouchableWithoutFeedback>
        )}
        <FloatingMenu isOpen={isMenuOpen} setIsOpen={setIsMenuOpen} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF",
  },
  content: {
    flex: 1,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    zIndex: 1,
  },
  section: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 20,
    textAlign: "left",
  },
  todayTaskWrapper: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  noDataContainer: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
  },
  noDataText: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#222",
    marginBottom: 40,
  },
  flatListContent: {
    flexGrow: 1,
  },

  quickActionsSection: {
    marginTop: 20,
  },
  quickActionsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 12,
  },
  quickActionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
    backgroundColor: CrimsonLuxe.primary400,
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  quickActionText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
    textAlign: "center",
  },
});

export default Home;
