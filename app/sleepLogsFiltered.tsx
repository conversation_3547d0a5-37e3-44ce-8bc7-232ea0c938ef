import React, { useCallback, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  ActivityIndicator,
} from "react-native";
import { useFocusEffect, router, useLocalSearchParams } from "expo-router";
import PageLayout from "@/components/pageLayout";
import BackButtonHeader from "@/components/backButtonHeader";
import SleepLogCard from "@/components/sleepLogCard";
import { CrimsonLuxe } from "@/constants/Colors";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import SleepSVG from "@/assets/svgs/Sleep.svg";
import {
  getSleeplogsByPeriod,
  getSleeplogStats,
} from "@/db/service/SleeplogService";

interface SleepLog {
  id: string;
  title: string;
  sleepTime: Date;
  wakeTime?: Date;
  isCompleted: boolean;
  notes?: string;
  duration?: number;
  quality?: number;
  autoQuality?: number;
  createdAt: Date;
}

const SleepLogsFilteredScreen = () => {
  const { period } = useLocalSearchParams<{ period: "weekly" | "monthly" | "allTime" }>();
  const [sleepLogs, setSleepLogs] = useState<SleepLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    totalDuration: 0,
    averageQuality: 0,
  });

  const fetchSleepLogs = async () => {
    try {
      const [logs, periodStats] = await Promise.all([
        getSleeplogsByPeriod(period || "weekly"),
        getSleeplogStats(period || "weekly"),
      ]);


      setSleepLogs(logs);
      setStats(periodStats);
    } catch (error) {
      console.error("Error fetching filtered sleep logs:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchSleepLogs();
    }, [period])
  );

  const onRefresh = () => {
    setRefreshing(true);
    fetchSleepLogs();
  };

  const handleSleepLogPress = (sleepLog: SleepLog) => {
    router.push({
      pathname: "/sleepLogDetail",
      params: { id: sleepLog.id },
    });
  };

  const getPeriodLabel = () => {
    switch (period) {
      case "weekly":
        return "This Week";
      case "monthly":
        return "This Month";
      case "allTime":
        return "All Time";
      default:
        return "Sleep Logs";
    }
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return "0h 0m";
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  if (loading) {
    return (
      <PageLayout style={styles.container}>
        <BackButtonHeader title={`${getPeriodLabel()} Sleep Logs`} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={CrimsonLuxe.primary400} />
          <Text style={styles.loadingText}>Loading sleep logs...</Text>
        </View>
      </PageLayout>
    );
  }

  const renderStatsCard = () => (
    <View style={styles.statsCard}>
      <Text style={styles.statsTitle}>{getPeriodLabel()} Summary</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <MaterialCommunityIcons name="sleep" size={24} color={CrimsonLuxe.primary400} />
          <Text style={styles.statValue}>{stats.total}</Text>
          <Text style={styles.statLabel}>Sleep Sessions</Text>
        </View>
        <View style={styles.statItem}>
          <MaterialCommunityIcons name="clock-outline" size={24} color="#4CAF50" />
          <Text style={styles.statValue}>{formatDuration(stats.totalDuration)}</Text>
          <Text style={styles.statLabel}>Total Sleep</Text>
        </View>
        <View style={styles.statItem}>
          <MaterialCommunityIcons name="star" size={24} color="#FFD700" />
          <Text style={styles.statValue}>{stats.averageQuality.toFixed(1)}</Text>
          <Text style={styles.statLabel}>Avg Quality</Text>
        </View>
        <View style={styles.statItem}>
          <MaterialCommunityIcons name="chart-line" size={24} color="#FF9800" />
          <Text style={styles.statValue}>
            {stats.total > 0 ? Math.round(stats.totalDuration / stats.total) : 0}m
          </Text>
          <Text style={styles.statLabel}>Avg Duration</Text>
        </View>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <SleepSVG width={"90%"} height={300} />
      <Text style={styles.emptyTitle}>No Sleep Logs Found</Text>
      <Text style={styles.emptySubtitle}>
        No sleep logs found for {getPeriodLabel().toLowerCase()}. Start tracking your sleep patterns!
      </Text>
      <TouchableOpacity
        style={styles.createButton}
        onPress={() => router.push("/createSleepLog")}
      >
        <Text style={styles.createButtonText}>Create Sleep Log</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSleepLog = ({ item }: { item: SleepLog }) => (
    <SleepLogCard
      sleeplog={item}
      onPress={() => handleSleepLogPress(item)}
      onUpdate={fetchSleepLogs}
      showQuality={true}
    />
  );

  return (
    <PageLayout style={styles.container} scrollable={false}>
      <BackButtonHeader title={`${getPeriodLabel()} Sleep Logs`} />

      <View style={styles.periodIndicator}>
        <MaterialCommunityIcons
          name="filter-variant"
          size={16}
          color={CrimsonLuxe.primary400}
        />
        <Text style={styles.periodText}>
          Showing {sleepLogs.length} sleep log{sleepLogs.length !== 1 ? 's' : ''} for {getPeriodLabel().toLowerCase()}
        </Text>
      </View>
      
      <View style={styles.headerActions}>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push("/createSleepLog")}
        >
          <MaterialCommunityIcons name="plus" size={20} color="#fff" />
          <Text style={styles.addButtonText}>Add Log</Text>
        </TouchableOpacity>
      </View>

      {sleepLogs.length > 0 && renderStatsCard()}

      <View style={styles.flatListContainer}>
        <FlatList
          data={sleepLogs}
          keyExtractor={(item) => item.id}
          renderItem={renderSleepLog}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[CrimsonLuxe.primary400]}
              tintColor={CrimsonLuxe.primary400}
              progressBackgroundColor="#fff"
            />
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={
            sleepLogs.length === 0 ? styles.emptyListContainer : styles.listContainer
          }
          ItemSeparatorComponent={() => <View style={styles.itemSeparator} />}
          ListFooterComponent={() => <View style={styles.listFooter} />}
          ListHeaderComponent={sleepLogs.length > 0 ? () => <View style={styles.listHeader} /> : null}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          updateCellsBatchingPeriod={50}
          initialNumToRender={8}
          windowSize={10}
          bounces={true}
          bouncesZoom={false}
          scrollEventThrottle={16}
          style={styles.flatList}
          nestedScrollEnabled={true}
        />
      </View>
    </PageLayout>
  );
};

export default SleepLogsFilteredScreen;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF",
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
    fontWeight: "500",
  },
  periodIndicator: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: `${CrimsonLuxe.primary400}08`,
    borderRadius: 8,
    gap: 8,
  },
  periodText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  headerActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginHorizontal: 16,
    marginVertical: 12,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: CrimsonLuxe.primary400,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 14,
  },
  statsCard: {
    backgroundColor: "#fff",
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 16,
    textAlign: "center",
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "center",
    width: "48%",
    marginBottom: 16,
  },
  statValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
  flatListContainer: {
    flex: 1,
  },
  flatList: {
    flex: 1,
  },
  emptyListContainer: {
    flex: 1,
  },
  listContainer: {
    paddingBottom: 20,
  },
  listHeader: {
    height: 8,
  },
  listFooter: {
    height: 20,
  },
  itemSeparator: {
    height: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 24,
  },
  createButton: {
    backgroundColor: CrimsonLuxe.primary400,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});
