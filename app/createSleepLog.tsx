import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Keyboard,
  Modal,
  TouchableWithoutFeedback,
  Alert,
} from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import PageLayout from "@/components/pageLayout";
import BackButtonHeader from "@/components/backButtonHeader";
import CustomTextInput from "@/components/textInput";
import CustomTextArea from "@/components/textArea";
import { CrimsonLuxe } from "@/constants/Colors";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { createSleeplog } from "@/db/service/SleeplogService";
import { router } from "expo-router";

const CreateSleepLogScreen = () => {
  const [sleepTime, setSleepTime] = useState(new Date());
  const [notes, setNotes] = useState("");

  const [showSleepTimePicker, setShowSleepTimePicker] = useState(false);



  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const handleCreateSleepLog = async () => {
    try {
      const sleepLogData = {
        sleepTime,
        wakeTime: undefined, // No wake time set initially
        isCompleted: false, // Always incomplete when going to sleep
        notes: notes.trim() || undefined,
        duration: 0, // No duration yet
        quality: undefined, // No quality until wake up
        createdAt: new Date(),
      };

      const sleepLogId = await createSleeplog(sleepLogData);

      if (sleepLogId) {
        Alert.alert(
          "Sleep Log Created",
          "Sweet dreams! 😴",
          [
            {
              text: "OK",
              onPress: () => router.back(),
            },
          ]
        );
      }
    } catch (error) {
      console.error("Failed to create sleep log:", error);
      Alert.alert("Error", "Failed to create sleep log. Please try again.");
    }
  };

  const handleCancel = () => {
    setNotes("");
    setSleepTime(new Date());
    router.back();
  };



  const renderTimeSelector = (
    label: string,
    time: Date,
    onPress: () => void,
    icon: React.ComponentProps<typeof MaterialCommunityIcons>['name']
  ) => (
    <View style={styles.timeField}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity style={styles.timeButton} onPress={onPress}>
        <MaterialCommunityIcons name={icon} size={20} color="#666" />
        <Text style={styles.timeText}>{formatTime(time)}</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <PageLayout style={styles.container}>
      <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
        <View style={styles.content}>
          <BackButtonHeader title="Create Sleep Log" />

          <Text style={styles.helpText}>
            💤 Log when you're about to sleep. When you wake up to add your wake time and get automatic quality rating!
          </Text>

          <View style={styles.singleTimeContainer}>
            {renderTimeSelector(
              "Sleep Time",
              sleepTime,
              () => setShowSleepTimePicker(true),
              "power-sleep"
            )}
          </View>



          <Text style={styles.label}>Notes (Optional)</Text>
          <CustomTextArea
            placeholder="How did you sleep? Any observations..."
            value={notes}
            onChangeText={setNotes}
            multiline
            maxLength={200}
          />
          <Text style={styles.charCount}>{notes.length}/200</Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.createButton, { backgroundColor: CrimsonLuxe.primary400 }]}
              onPress={handleCreateSleepLog}
            >
              <Text style={styles.createText}>😴 Log Sleep Time</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableWithoutFeedback>

      {/* Sleep Time Picker */}
      {showSleepTimePicker && Platform.OS === "android" && (
        <DateTimePicker
          value={sleepTime}
          mode="time"
          display="default"
          onChange={(event, selectedTime) => {
            setShowSleepTimePicker(false);
            if (selectedTime) {
              setSleepTime(selectedTime);
            }
          }}
        />
      )}

      {Platform.OS === "ios" && (
        <Modal
          transparent
          animationType="slide"
          visible={showSleepTimePicker}
          onRequestClose={() => setShowSleepTimePicker(false)}
        >
          <TouchableWithoutFeedback onPress={() => setShowSleepTimePicker(false)}>
            <View style={styles.modalOverlay}>
              <View style={styles.pickerContainer}>
                <DateTimePicker
                  value={sleepTime}
                  mode="time"
                  display="spinner"
                  onChange={(event, selectedTime) => {
                    if (selectedTime) setSleepTime(selectedTime);
                  }}
                />
                <TouchableOpacity
                  style={styles.doneButton}
                  onPress={() => setShowSleepTimePicker(false)}
                >
                  <Text style={styles.doneText}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}


    </PageLayout>
  );
};

export default CreateSleepLogScreen;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF",
    flex: 1,
  },
  content: {
    flex: 1,
  },
  label: {
    fontSize: 16,
    color: "#333",
    marginBottom: 5,
    marginTop: 15,
  },
  charCount: {
    alignSelf: "flex-end",
    fontSize: 12,
    color: "#999",
    marginTop: 2,
  },
  logTypeContainer: {
    flexDirection: "row",
    marginVertical: 10,
    gap: 12,
  },
  logTypeButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#DDD",
    gap: 8,
  },
  selectedLogType: {
    backgroundColor: CrimsonLuxe.primary400,
    borderColor: CrimsonLuxe.primary400,
  },
  logTypeText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  selectedLogTypeText: {
    color: "#fff",
    fontWeight: "600",
  },
  helpText: {
    fontSize: 13,
    color: "#666",
    fontStyle: "italic",
    textAlign: "center",
    marginTop: 8,
    marginBottom: 4,
    lineHeight: 18,
  },
  singleTimeContainer: {
    marginTop: 10,
  },
  timeField: {
    marginBottom: 10,
  },
  timeButton: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#DDD",
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 14,
    gap: 8,
  },
  timeText: {
    fontSize: 16,
    color: "#333",
  },

  completedContainer: {
    marginVertical: 15,
  },
  completedToggle: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  completedText: {
    fontSize: 16,
    color: "#333",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 30,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: "center",
  },
  cancelText: {
    color: "#333",
    fontSize: 16,
    fontWeight: "500",
  },
  createButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: "center",
  },
  createText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "flex-end",
  },
  pickerContainer: {
    backgroundColor: "#fff",
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  doneButton: {
    alignItems: "center",
    marginTop: 10,
  },
  doneText: {
    color: CrimsonLuxe.primary400,
    fontSize: 18,
    fontWeight: "bold",
  },
});
