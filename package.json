{"name": "pocketmini", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.1.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "dayjs": "^1.11.13", "expo": "^53.0.20", "expo-auth-session": "~6.2.1", "expo-blur": "~14.1.5", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.6", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-navigation-bar": "~4.2.7", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "firebase": "^11.3.1", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-calendars": "^1.1310.0", "react-native-chart-kit": "^6.12.0", "react-native-circular-progress": "^1.4.1", "react-native-draggable-flatlist": "^4.0.1", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.13.1", "react-native-pell-rich-editor": "^1.9.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-signature-canvas": "^4.7.2", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "realm": "^12.14.1", "zustand": "^5.0.3", "react-test-renderer": "^19.0.0", "@types/react-test-renderer": "^19.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "jest": "^29.2.1", "jest-expo": "~53.0.9", "react-native-svg-transformer": "^1.5.0", "typescript": "^5.7.3"}, "private": true}