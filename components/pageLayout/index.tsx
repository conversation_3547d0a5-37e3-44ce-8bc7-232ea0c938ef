import React, { ReactNode, useEffect } from "react";
import {
  ScrollView,
  SafeAreaView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  ViewStyle,
  StatusBarStyle,
  View,
} from "react-native";
import * as NavigationBar from "expo-navigation-bar";
interface PageProps {
  children: ReactNode;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  backgroundColor?: any;
  statusBarColor?: StatusBarStyle;
  scrollable?: boolean; // New prop to control scrolling
}

const PageLayout: React.FC<PageProps> = ({
  children,
  style,
  contentContainerStyle,
  backgroundColor = "#FFFFFF",
  statusBarColor = "dark-content",
  scrollable = true, // Default to true for backward compatibility
}) => {
  useEffect(() => {
    if (Platform.OS === "android") {
      NavigationBar.setBackgroundColorAsync(backgroundColor);
      NavigationBar.setButtonStyleAsync(
        statusBarColor === "dark-content" ? "dark" : "light"
      );
    }
  }, [backgroundColor, statusBarColor]);
  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor }, style]}>
      <StatusBar barStyle={statusBarColor} backgroundColor={backgroundColor} />
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        {scrollable ? (
          <ScrollView
            contentContainerStyle={[
              styles.scrollViewContent,
              contentContainerStyle,
            ]}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {children}
          </ScrollView>
        ) : (
          <View style={[styles.scrollViewContent, contentContainerStyle]}>
            {children}
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: 16,
  },
});

export default PageLayout;
