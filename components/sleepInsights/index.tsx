import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { CrimsonLuxe } from "@/constants/Colors";

interface SleepInsightsProps {
  insights: string[];
  recommendations: string[];
  sleepType: string;
  optimalDuration: string;
  optimalBedtime: string;
  optimalWakeTime: string;
}

const SleepInsights: React.FC<SleepInsightsProps> = ({
  insights,
  recommendations,
  sleepType,
  optimalDuration,
  optimalBedtime,
  optimalWakeTime,
}) => {
  const renderInsightItem = (insight: string, index: number) => {
    const isPositive = insight.includes("✅") || insight.includes("🌟") || insight.includes("🌙") || insight.includes("🌅");
    const isWarning = insight.includes("⚠️") || insight.includes("😴") || insight.includes("⏰");
    
    let iconName = "information-outline";
    let iconColor = CrimsonLuxe.primary400;
    
    if (isPositive) {
      iconName = "check-circle-outline";
      iconColor = "#4CAF50";
    } else if (isWarning) {
      iconName = "alert-circle-outline";
      iconColor = "#FF9800";
    }

    return (
      <View key={index} style={styles.insightItem}>
        <MaterialCommunityIcons 
          name={iconName} 
          size={16} 
          color={iconColor} 
          style={styles.insightIcon}
        />
        <Text style={styles.insightText}>{insight}</Text>
      </View>
    );
  };

  const renderRecommendationItem = (recommendation: string, index: number) => (
    <View key={index} style={styles.recommendationItem}>
      <MaterialCommunityIcons 
        name="lightbulb-outline" 
        size={16} 
        color={CrimsonLuxe.primary400} 
        style={styles.recommendationIcon}
      />
      <Text style={styles.recommendationText}>{recommendation}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Sleep Type Badge */}
      <View style={styles.sleepTypeBadge}>
        <MaterialCommunityIcons 
          name={sleepType === "Night Sleep" ? "weather-night" : "power-sleep"} 
          size={16} 
          color={CrimsonLuxe.primary400}
        />
        <Text style={styles.sleepTypeText}>{sleepType}</Text>
      </View>

      {/* Insights Section */}
      {insights.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📊 Sleep Analysis</Text>
          {insights.map(renderInsightItem)}
        </View>
      )}

      {/* Recommendations Section */}
      {recommendations.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💡 Recommendations</Text>
          {recommendations.map(renderRecommendationItem)}
        </View>
      )}

      {/* Optimal Guidelines */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🎯 Optimal Guidelines</Text>
        
        <View style={styles.guidelineItem}>
          <MaterialCommunityIcons name="clock-outline" size={16} color="#4CAF50" />
          <Text style={styles.guidelineLabel}>Duration:</Text>
          <Text style={styles.guidelineValue}>{optimalDuration}</Text>
        </View>
        
        <View style={styles.guidelineItem}>
          <MaterialCommunityIcons name="weather-sunset-down" size={16} color="#FF9800" />
          <Text style={styles.guidelineLabel}>Bedtime:</Text>
          <Text style={styles.guidelineValue}>{optimalBedtime}</Text>
        </View>
        
        <View style={styles.guidelineItem}>
          <MaterialCommunityIcons name="weather-sunset-up" size={16} color="#FFD700" />
          <Text style={styles.guidelineLabel}>Wake Time:</Text>
          <Text style={styles.guidelineValue}>{optimalWakeTime}</Text>
        </View>
      </View>
    </View>
  );
};

export default SleepInsights;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  sleepTypeBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${CrimsonLuxe.primary400}20`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: "flex-start",
    marginBottom: 16,
  },
  sleepTypeText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: "600",
    color: CrimsonLuxe.primary400,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
  },
  insightItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8,
    paddingVertical: 4,
  },
  insightIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  insightText: {
    flex: 1,
    fontSize: 14,
    color: "#555",
    lineHeight: 20,
  },
  recommendationItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8,
    paddingVertical: 4,
    backgroundColor: "#F8F9FA",
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  recommendationIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  recommendationText: {
    flex: 1,
    fontSize: 14,
    color: "#555",
    lineHeight: 20,
  },
  guidelineItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    paddingVertical: 4,
  },
  guidelineLabel: {
    marginLeft: 8,
    fontSize: 14,
    color: "#666",
    minWidth: 80,
  },
  guidelineValue: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    marginLeft: 8,
  },
});
