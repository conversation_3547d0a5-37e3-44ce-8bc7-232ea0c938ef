import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface SleepQualityDisplayProps {
  quality: number; // 1-5 scale
  showLabel?: boolean;
  showStars?: boolean;
  showEmoji?: boolean;
  size?: "small" | "medium" | "large";
  style?: any;
}

const SleepQualityDisplay: React.FC<SleepQualityDisplayProps> = ({
  quality,
  showLabel = true,
  showStars = true,
  showEmoji = false,
  size = "medium",
  style,
}) => {
  const getQualityColor = (quality: number) => {
    if (quality >= 4.5) return "#4CAF50"; // Excellent - Green
    if (quality >= 3.5) return "#8BC34A"; // Very Good - Light Green
    if (quality >= 2.5) return "#FFC107"; // Good - Yellow
    if (quality >= 1.5) return "#FF9800"; // Fair - Orange
    return "#F44336"; // Poor - Red
  };

  const getQualityLabel = (quality: number) => {
    if (quality >= 4.5) return "Excellent";
    if (quality >= 3.5) return "Very Good";
    if (quality >= 2.5) return "Good";
    if (quality >= 1.5) return "Fair";
    return "Poor";
  };

  const getQualityEmoji = (quality: number) => {
    if (quality >= 4.5) return "😴✨"; // Excellent
    if (quality >= 3.5) return "😊"; // Very Good
    if (quality >= 2.5) return "😐"; // Good
    if (quality >= 1.5) return "😪"; // Fair
    return "😵"; // Poor
  };

  const getSizeStyles = () => {
    switch (size) {
      case "small":
        return {
          starSize: 12,
          fontSize: 12,
          emojiSize: 16,
        };
      case "large":
        return {
          starSize: 20,
          fontSize: 18,
          emojiSize: 24,
        };
      default: // medium
        return {
          starSize: 16,
          fontSize: 14,
          emojiSize: 20,
        };
    }
  };

  const sizeStyles = getSizeStyles();
  const qualityColor = getQualityColor(quality);

  const renderStars = () => {
    if (!showStars) return null;

    return (
      <View style={styles.starContainer}>
        {[1, 2, 3, 4, 5].map((star) => {
          let starName = "star-outline";
          let starColor = "#DDD";

          if (star <= Math.floor(quality)) {
            // Full star
            starName = "star";
            starColor = qualityColor;
          } else if (star === Math.floor(quality) + 1 && quality % 1 >= 0.5) {
            // Half star (using star-half for better visual representation)
            starName = "star-half";
            starColor = qualityColor;
          }

          return (
            <Ionicons
              key={star}
              name={starName}
              size={sizeStyles.starSize}
              color={starColor}
              style={styles.star}
            />
          );
        })}
      </View>
    );
  };

  const renderLabel = () => {
    if (!showLabel) return null;

    return (
      <Text
        style={[
          styles.qualityLabel,
          { color: qualityColor, fontSize: sizeStyles.fontSize },
        ]}
      >
        {getQualityLabel(quality)}
      </Text>
    );
  };

  const renderEmoji = () => {
    if (!showEmoji) return null;

    return (
      <Text style={[styles.emoji, { fontSize: sizeStyles.emojiSize }]}>
        {getQualityEmoji(quality)}
      </Text>
    );
  };

  const renderNumericRating = () => {
    return (
      <View style={styles.numericContainer}>
        <Text
          style={[
            styles.numericRating,
            { color: qualityColor, fontSize: sizeStyles.fontSize },
          ]}
        >
          {quality.toFixed(1)}
        </Text>
        <Text style={[styles.maxRating, { fontSize: sizeStyles.fontSize - 2 }]}>
          /5
        </Text>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {showEmoji && renderEmoji()}
      {showStars && renderStars()}
      {renderNumericRating()}
      {showLabel && renderLabel()}
    </View>
  );
};

export default SleepQualityDisplay;

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  starContainer: {
    flexDirection: "row",
    marginVertical: 2,
  },
  star: {
    marginHorizontal: 1,
  },
  qualityLabel: {
    fontWeight: "600",
    marginTop: 4,
  },
  emoji: {
    marginBottom: 4,
  },
  numericContainer: {
    flexDirection: "row",
    alignItems: "baseline",
    marginVertical: 2,
  },
  numericRating: {
    fontWeight: "bold",
  },
  maxRating: {
    color: "#888",
    marginLeft: 2,
  },
});

// Export utility functions for use elsewhere
export const getQualityColor = (quality: number) => {
  if (quality >= 4.5) return "#4CAF50";
  if (quality >= 3.5) return "#8BC34A";
  if (quality >= 2.5) return "#FFC107";
  if (quality >= 1.5) return "#FF9800";
  return "#F44336";
};

export const getQualityLabel = (quality: number) => {
  if (quality >= 4.5) return "Excellent";
  if (quality >= 3.5) return "Very Good";
  if (quality >= 2.5) return "Good";
  if (quality >= 1.5) return "Fair";
  return "Poor";
};
