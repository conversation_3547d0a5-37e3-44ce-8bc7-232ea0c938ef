import Realm from "realm";
import PomodoroSchema from "./schema/PomodoroSchema";
import { ChecklistSchema, TaskSchema } from "./schema/ChecklistSchema";
import { EventSchema } from "./schema/EventSchema";
import { UserPreferencesSchema, UserSchema } from "./schema/UserSchema";
import SleeplogSchema from "./schema/SleeplogSchema";

let realmInstance: Realm | null = null;

export const getRealm = async (): Promise<Realm> => {
  if (realmInstance) return realmInstance;

  realmInstance = await Realm.open({
    schema: [
      PomodoroSchema,
      ChecklistSchema,
      EventSchema,
      TaskSchema,
      UserSchema,
      UserPreferencesSchema,
      SleeplogSchema,
    ],
    schemaVersion: 20, // Increment version for migration
    onMigration: (oldRealm: Realm, newRealm: Realm) => {
      // Handle migration from version 19 to 20
      if (oldRealm.schemaVersion < 20) {
        // Get all old Sleeplog objects
        const oldSleeplogs = oldRealm.objects('Sleeplog');
        const newSleeplogs = newRealm.objects('Sleeplog');

        // For each sleeplog, handle the wakeTime property change
        for (let i = 0; i < oldSleeplogs.length; i++) {
          const oldSleeplog = oldSleeplogs[i] as any;
          const newSleeplog = newSleeplogs[i] as any;

          // If the sleep log is not completed, set wakeTime to null
          // If it's completed but wakeTime equals sleepTime (placeholder), also set to null
          if (!oldSleeplog.isCompleted ||
              (oldSleeplog.wakeTime && oldSleeplog.sleepTime &&
               oldSleeplog.wakeTime.getTime() === oldSleeplog.sleepTime.getTime())) {
            newSleeplog.wakeTime = null;
          }
        }
      }
    },
  });

  return realmInstance;
};
