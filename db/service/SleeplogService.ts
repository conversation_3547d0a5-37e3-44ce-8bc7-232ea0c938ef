import Realm from "realm";
import { getRealm } from "../realm";

// Helper function to generate title based on sleep data
const generateSleepTitle = (sleepTime: Date, isCompleted: boolean) => {
  try {
    // Ensure sleepTime is a valid Date object
    const date = sleepTime instanceof Date ? sleepTime : new Date(sleepTime);

    if (isNaN(date.getTime())) {
      // If date is invalid, return default title
      return isCompleted ? "Sleep Session" : "Sleep Time";
    }

    const hour = date.getHours();

    if (isCompleted) {
      // For completed sleep sessions
      if (hour >= 22 || hour <= 6) return "Night Sleep";
      if (hour >= 12 && hour <= 16) return "Afternoon Nap";
      return "Sleep Session";
    } else {
      // For going to sleep logs
      if (hour >= 22 || hour <= 6) return "Going to Sleep";
      if (hour >= 12 && hour <= 16) return "Nap Time";
      return "Sleep Time";
    }
  } catch (error) {
    console.error("Error generating sleep title:", error);
    return isCompleted ? "Sleep Session" : "Sleep Time";
  }
};

// Helper function to determine if it's a nap or night sleep
const isNightSleep = (sleepTime: Date, duration: number) => {
  const hour = sleepTime.getHours();
  // Consider it night sleep if:
  // 1. Sleep time is between 8 PM and 10 AM, OR
  // 2. Duration is longer than 4 hours (240 minutes)
  return (hour >= 20 || hour <= 10) && duration >= 180; // At least 3 hours for night sleep
};

// Calculate sleep efficiency score based on duration
const calculateDurationScore = (duration: number, sleepTime: Date) => {
  const isNight = isNightSleep(sleepTime, duration);

  if (isNight) {
    // Night sleep scoring with more realistic curves
    if (duration < 240) return 1.0; // Less than 4 hours - Poor
    if (duration < 300) return 1.5; // 4-5 hours - Poor to Fair
    if (duration < 360) return 2.5; // 5-6 hours - Fair
    if (duration < 420) return 3.5; // 6-7 hours - Good
    if (duration <= 540) return 5.0; // 7-9 hours - Excellent (optimal range)
    if (duration <= 600) return 4.5; // 9-10 hours - Very Good
    if (duration <= 660) return 3.5; // 10-11 hours - Good (might be oversleeping)
    return 2.5; // More than 11 hours - Fair (likely oversleeping)
  } else {
    // Nap scoring
    if (duration < 10) return 1.0; // Less than 10 minutes - Too short
    if (duration <= 30) return 5.0; // 10-30 minutes - Power nap (excellent)
    if (duration <= 90) return 4.5; // 30-90 minutes - Good nap
    if (duration <= 120) return 3.0; // 90-120 minutes - Decent but might cause grogginess
    return 2.0; // Longer than 2 hours - Likely to interfere with night sleep
  }
};

// Calculate circadian rhythm alignment score
const calculateCircadianScore = (sleepTime: Date, wakeTime: Date) => {
  const sleepHour = sleepTime.getHours();
  const sleepMinute = sleepTime.getMinutes();
  const wakeHour = wakeTime.getHours();
  const wakeMinute = wakeTime.getMinutes();

  // Convert to decimal hours for easier calculation
  const sleepDecimal = sleepHour + sleepMinute / 60;
  const wakeDecimal = wakeHour + wakeMinute / 60;

  let score = 3.0; // Base score

  // Optimal sleep window: 9:30 PM - 10:30 PM (21.5 - 22.5)
  if (sleepDecimal >= 21.5 && sleepDecimal <= 22.5) {
    score += 1.5; // Excellent sleep timing
  } else if ((sleepDecimal >= 21.0 && sleepDecimal < 21.5) || (sleepDecimal > 22.5 && sleepDecimal <= 23.0)) {
    score += 1.0; // Good sleep timing
  } else if ((sleepDecimal >= 20.5 && sleepDecimal < 21.0) || (sleepDecimal > 23.0 && sleepDecimal <= 23.5)) {
    score += 0.5; // Fair sleep timing
  } else if (sleepDecimal > 23.5 || (sleepDecimal > 2.0 && sleepDecimal < 20.5)) {
    score -= 1.0; // Poor sleep timing (too late or daytime sleep)
  }

  // Optimal wake window: 6:00 AM - 8:00 AM
  if (wakeDecimal >= 6.0 && wakeDecimal <= 8.0) {
    score += 1.0; // Excellent wake timing
  } else if ((wakeDecimal >= 5.0 && wakeDecimal < 6.0) || (wakeDecimal > 8.0 && wakeDecimal <= 9.0)) {
    score += 0.5; // Good wake timing
  } else if (wakeDecimal > 10.0 || wakeDecimal < 5.0) {
    score -= 0.5; // Suboptimal wake timing
  }

  return Math.max(1.0, Math.min(5.0, score));
};

// Calculate sleep consistency score (placeholder for future enhancement)
const calculateConsistencyScore = (_sleepTime: Date) => {
  // For now, return neutral score
  // In future, this could analyze sleep patterns over time
  // The parameter is prefixed with _ to indicate it's intentionally unused for now
  return 3.0;
};

// Calculate weekend vs weekday adjustment
const calculateScheduleScore = (sleepTime: Date, wakeTime: Date) => {
  const dayOfWeek = sleepTime.getDay(); // 0 = Sunday, 6 = Saturday
  const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

  const sleepHour = sleepTime.getHours();
  const wakeHour = wakeTime.getHours();

  if (isWeekend) {
    // More lenient on weekends
    if (sleepHour <= 2 || sleepHour >= 21) return 3.5; // Later sleep is more acceptable
    if (wakeHour >= 7 && wakeHour <= 10) return 3.5; // Later wake is more acceptable
    return 3.0;
  } else {
    // Stricter on weekdays for work/school schedule
    if (sleepHour >= 21 && sleepHour <= 22) return 4.0; // Optimal weekday sleep time
    if (sleepHour === 23 || sleepHour === 20) return 3.5; // Good weekday sleep time
    if (wakeHour >= 6 && wakeHour <= 8) return 4.0; // Good weekday wake time
    if (sleepHour > 23 || sleepHour < 20) return 2.5; // Suboptimal weekday timing
    return 3.0;
  }
};

// Main function to calculate automatic sleep quality with improved algorithm
export const calculateAutoSleepQuality = (sleepTime: Date, wakeTime: Date, duration: number) => {
  try {
    if (!duration || duration <= 0) return 3; // Default if no duration

    // Ensure dates are valid Date objects
    const sleep = sleepTime instanceof Date ? sleepTime : new Date(sleepTime);
    const wake = wakeTime instanceof Date ? wakeTime : new Date(wakeTime);

    if (isNaN(sleep.getTime()) || isNaN(wake.getTime())) {
      return 3; // Default if invalid dates
    }

    // Calculate individual component scores
    const durationScore = calculateDurationScore(duration, sleep);
    const circadianScore = calculateCircadianScore(sleep, wake);
    const consistencyScore = calculateConsistencyScore(sleep);
    const scheduleScore = calculateScheduleScore(sleep, wake);

    // Weighted calculation based on sleep science research
    const weights = {
      duration: 0.40,      // Duration is most important
      circadian: 0.35,     // Timing with circadian rhythm is crucial
      schedule: 0.15,      // Weekend vs weekday considerations
      consistency: 0.10    // Sleep consistency (placeholder for future)
    };

    const weightedScore =
      (durationScore * weights.duration) +
      (circadianScore * weights.circadian) +
      (scheduleScore * weights.schedule) +
      (consistencyScore * weights.consistency);

    // Apply additional modifiers
    let finalScore = weightedScore;

    // Bonus for optimal sleep duration in optimal time window
    if (duration >= 420 && duration <= 540 &&
        sleep.getHours() >= 21 && sleep.getHours() <= 22 &&
        wake.getHours() >= 6 && wake.getHours() <= 8) {
      finalScore += 0.2; // Small bonus for perfect sleep
    }

    // Penalty for very short sleep on weekdays
    const isWeekday = sleep.getDay() >= 1 && sleep.getDay() <= 5;
    if (isWeekday && duration < 360) { // Less than 6 hours on weekday
      finalScore -= 0.3;
    }

    // Round to nearest 0.5 for more nuanced scoring
    const roundedScore = Math.round(finalScore * 2) / 2;

    return Math.max(1, Math.min(5, roundedScore));
  } catch (error) {
    console.error("Error calculating auto sleep quality:", error);
    return 3; // Default quality on error
  }
};

// Helper function to get sleep quality insights and recommendations
export const getSleepQualityInsights = (sleepTime: Date, wakeTime: Date, duration: number, quality: number) => {
  const insights: string[] = [];
  const recommendations: string[] = [];

  const sleep = sleepTime instanceof Date ? sleepTime : new Date(sleepTime);
  const wake = wakeTime instanceof Date ? wakeTime : new Date(wakeTime);
  const sleepHour = sleep.getHours();
  const wakeHour = wake.getHours();
  const isNight = isNightSleep(sleep, duration);

  // Duration insights
  if (isNight) {
    if (duration < 360) {
      insights.push("⚠️ Short sleep duration detected");
      recommendations.push("Aim for 7-9 hours of sleep for optimal health");
    } else if (duration > 600) {
      insights.push("😴 Long sleep duration");
      recommendations.push("Consider if you're catching up on sleep debt or if you need to adjust your schedule");
    } else if (duration >= 420 && duration <= 540) {
      insights.push("✅ Optimal sleep duration achieved");
    }
  } else {
    if (duration > 120) {
      insights.push("⏰ Long nap detected");
      recommendations.push("Long naps may interfere with nighttime sleep. Try limiting naps to 20-30 minutes");
    } else if (duration >= 20 && duration <= 30) {
      insights.push("✅ Perfect power nap duration");
    }
  }

  // Timing insights
  if (sleepHour >= 21 && sleepHour <= 22) {
    insights.push("🌙 Excellent bedtime alignment with circadian rhythm");
  } else if (sleepHour === 23 || sleepHour === 20) {
    insights.push("🌙 Good bedtime alignment with circadian rhythm");
  } else if (sleepHour > 23 || sleepHour < 20) {
    insights.push("⏰ Bedtime could be optimized");
    recommendations.push("Try going to bed between 9:30-10:30 PM for better sleep quality");
  }

  if (wakeHour >= 6 && wakeHour <= 8) {
    insights.push("🌅 Good wake time for natural circadian rhythm");
  } else if (wakeHour > 9) {
    insights.push("😴 Late wake time");
    recommendations.push("Consider gradually moving your bedtime earlier to wake up earlier");
  }

  // Weekend vs weekday
  const dayOfWeek = sleep.getDay();
  const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

  if (isWeekend && (sleepHour > 23 || wakeHour > 9)) {
    insights.push("📅 Weekend sleep pattern detected");
    recommendations.push("Try to maintain consistent sleep times even on weekends for better sleep quality");
  }

  // Overall quality insights
  if (quality >= 4.5) {
    insights.push("🌟 Excellent sleep quality achieved!");
  } else if (quality < 2.5) {
    insights.push("😔 Sleep quality could be improved");
    recommendations.push("Consider factors like room temperature, noise, light exposure, and pre-sleep routine");
  }

  return {
    insights,
    recommendations,
    sleepType: isNight ? "Night Sleep" : "Nap",
    optimalDuration: isNight ? "7-9 hours" : "20-30 minutes",
    optimalBedtime: "9:30-10:30 PM",
    optimalWakeTime: "6:00-8:00 AM"
  };
};

// Helper function to calculate sleep debt (simplified version)
export const calculateSleepDebt = (recentSleepLogs: any[]) => {
  const targetNightSleep = 480; // 8 hours in minutes
  let totalDebt = 0;
  let nightSleepCount = 0;

  recentSleepLogs.forEach(log => {
    if (log.duration && log.sleepTime) {
      const sleepTime = new Date(log.sleepTime);
      if (isNightSleep(sleepTime, log.duration)) {
        const debt = Math.max(0, targetNightSleep - log.duration);
        totalDebt += debt;
        nightSleepCount++;
      }
    }
  });

  return {
    totalDebtMinutes: totalDebt,
    averageDebtPerNight: nightSleepCount > 0 ? totalDebt / nightSleepCount : 0,
    nightSleepCount
  };
};

export const createSleeplog = async ({
  sleepTime,
  wakeTime,
  isCompleted,
  notes,
  duration,
  quality,
  createdAt,
}: {
  sleepTime: Date;
  wakeTime?: Date;
  isCompleted: boolean;
  notes?: string;
  duration?: number;
  quality?: number;
  createdAt: Date;
}) => {
  try {
    const realm = await getRealm();
    let newSleeplogId = null;

    realm.write(() => {
      console.log("Creating sleeplog with data:", {
        sleepTime,
        wakeTime,
        isCompleted,
        notes,
        duration,
        quality,
        createdAt,
      });

      const sleeplog = realm.create("Sleeplog", {
        _id: new Realm.BSON.ObjectId(),
        sleepTime,
        wakeTime,
        isCompleted,
        notes,
        duration,
        quality,
        createdAt,
      });
      newSleeplogId = sleeplog._id;
      console.log("Sleeplog created successfully with ID:", newSleeplogId);
    });

    return newSleeplogId;
  } catch (error) {
    console.error("Error creating sleeplog:", error);
    throw error;
  }
};

export const getAllSleeplogs = async () => {
  const realm = await getRealm();
  const sleeplogs = realm.objects("Sleeplog").sorted("createdAt", true);

  return sleeplogs.map((sleeplog: any) => {
    // Ensure dates are properly converted
    const sleepTime = new Date(sleeplog.sleepTime);
    const wakeTime = sleeplog.wakeTime ? new Date(sleeplog.wakeTime) : undefined;
    const createdAt = new Date(sleeplog.createdAt);

    // Auto-calculate quality if not set and sleep is completed
    let quality = sleeplog.quality;
    if (!quality && sleeplog.isCompleted && sleeplog.duration && wakeTime) {
      quality = calculateAutoSleepQuality(sleepTime, wakeTime, sleeplog.duration);
    }

    return {
      id: sleeplog._id,
      title: generateSleepTitle(sleepTime, sleeplog.isCompleted),
      sleepTime: sleepTime,
      wakeTime: wakeTime,
      isCompleted: sleeplog.isCompleted,
      notes: sleeplog.notes,
      duration: sleeplog.duration,
      quality: quality,
      autoQuality: sleeplog.isCompleted && sleeplog.duration && wakeTime
        ? calculateAutoSleepQuality(sleepTime, wakeTime, sleeplog.duration)
        : undefined,
      createdAt: createdAt,
    };
  });
};

export const updateSleeplog = async (
  id: Realm.BSON.ObjectId,
  updates: Partial<{
    sleepTime: Date;
    wakeTime?: Date;
    isCompleted: boolean;
    notes?: string;
    duration?: number;
    quality?: number;
  }>
) => {
  const realm = await getRealm();
  try {
    const sleeplog = realm.objectForPrimaryKey("Sleeplog", id);
    if (sleeplog) {
      realm.write(() => {
        Object.assign(sleeplog, updates);
      });
    }
  } finally {
    // Optional cleanup
  }
};

export const deleteSleeplog = async (id: Realm.BSON.ObjectId) => {
  const realm = await getRealm();
  try {
    const sleeplog = realm.objectForPrimaryKey("Sleeplog", id);
    if (sleeplog) {
      realm.write(() => {
        realm.delete(sleeplog);
      });
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error deleting sleeplog:", error);
    return false;
  }
};

export const getSleeplogById = async (id: string) => {
  const realm = await getRealm();
  const sleeplog = realm.objectForPrimaryKey("Sleeplog", new Realm.BSON.ObjectId(id));
  if (sleeplog) {
    const data = JSON.parse(JSON.stringify(sleeplog));

    // Ensure dates are properly converted
    data.sleepTime = new Date(data.sleepTime);
    data.wakeTime = data.wakeTime ? new Date(data.wakeTime) : undefined;
    data.createdAt = new Date(data.createdAt);

    data.title = generateSleepTitle(data.sleepTime, data.isCompleted);
    return data;
  }
  return null;
};

export const getSleeplogCount = async () => {
  const realm = await getRealm();
  const allSleeplogs = realm.objects("Sleeplog");
  const total = allSleeplogs.length;
  const completed = allSleeplogs.filtered("isCompleted == true").length;
  const incomplete = total - completed;

  return {
    total,
    completed,
    incomplete,
  };
};

export const getSleeplogStats = async (
  groupBy: "weekly" | "monthly" | "allTime" = "weekly"
) => {
  const realm = await getRealm();
  const allSleeplogs = realm.objects("Sleeplog");

  if (groupBy === "allTime") {
    let total = 0;
    let totalDuration = 0;
    let averageQuality = 0;
    let qualityCount = 0;

    allSleeplogs.forEach((item: any) => {
      total += 1;
      if (item.duration) {
        totalDuration += item.duration;
      }
      if (item.quality) {
        averageQuality += item.quality;
        qualityCount += 1;
      }
    });

    return {
      total,
      totalDuration,
      averageQuality: qualityCount > 0 ? averageQuality / qualityCount : 0,
    };
  }

  // For weekly and monthly stats
  const now = new Date();
  let startDate: Date;

  if (groupBy === "weekly") {
    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  } else {
    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
  }

  const filteredSleeplogs = allSleeplogs.filtered(
    "createdAt >= $0",
    startDate
  );

  let total = 0;
  let totalDuration = 0;
  let averageQuality = 0;
  let qualityCount = 0;

  filteredSleeplogs.forEach((item: any) => {
    total += 1;
    if (item.duration) {
      totalDuration += item.duration;
    }
    if (item.quality) {
      averageQuality += item.quality;
      qualityCount += 1;
    }
  });

  return {
    total,
    totalDuration,
    averageQuality: qualityCount > 0 ? averageQuality / qualityCount : 0,
    period: groupBy,
  };
};

export const getRecentSleeplogs = async (limit: number = 10) => {
  const realm = await getRealm();
  const recentSleeplogs = realm
    .objects("Sleeplog")
    .sorted("createdAt", true)
    .slice(0, limit);

  return recentSleeplogs.map((sleeplog: any) => {
    // Ensure dates are properly converted
    const sleepTime = new Date(sleeplog.sleepTime);
    const wakeTime = sleeplog.wakeTime ? new Date(sleeplog.wakeTime) : undefined;
    const createdAt = new Date(sleeplog.createdAt);

    return {
      id: sleeplog._id,
      title: generateSleepTitle(sleepTime, sleeplog.isCompleted),
      sleepTime: sleepTime,
      wakeTime: wakeTime,
      isCompleted: sleeplog.isCompleted,
      notes: sleeplog.notes,
      duration: sleeplog.duration,
      quality: sleeplog.quality,
      createdAt: createdAt,
    };
  });
};

export const getSleeplogsByDateRange = async (
  startDate: Date,
  endDate: Date
) => {
  const realm = await getRealm();
  const sleeplogs = realm
    .objects("Sleeplog")
    .filtered("createdAt >= $0 AND createdAt <= $1", startDate, endDate)
    .sorted("createdAt", true);

  return sleeplogs.map((sleeplog: any) => {
    // Ensure dates are properly converted
    const sleepTime = new Date(sleeplog.sleepTime);
    const wakeTime = sleeplog.wakeTime ? new Date(sleeplog.wakeTime) : undefined;
    const createdAt = new Date(sleeplog.createdAt);

    return {
      id: sleeplog._id,
      title: generateSleepTitle(sleepTime, sleeplog.isCompleted),
      sleepTime: sleepTime,
      wakeTime: wakeTime,
      isCompleted: sleeplog.isCompleted,
      notes: sleeplog.notes,
      duration: sleeplog.duration,
      quality: sleeplog.quality,
      createdAt: createdAt,
    };
  });
};

// Get sleep logs filtered by period (weekly, monthly, allTime)
export const getSleeplogsByPeriod = async (
  period: "weekly" | "monthly" | "allTime" = "weekly"
) => {
  const realm = await getRealm();
  let filteredSleeplogs;

  if (period === "allTime") {
    filteredSleeplogs = realm.objects("Sleeplog").sorted("createdAt", true);
  } else {
    const now = new Date();
    let startDate: Date;

    if (period === "weekly") {
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    } else {
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    filteredSleeplogs = realm.objects("Sleeplog")
      .filtered("createdAt >= $0", startDate)
      .sorted("createdAt", true);
  }

  // Convert Realm Results to array and map
  const sleeplogArray = Array.from(filteredSleeplogs);

  return sleeplogArray.map((sleeplog: any) => {
    const sleepTime = new Date(sleeplog.sleepTime);
    const wakeTime = sleeplog.wakeTime ? new Date(sleeplog.wakeTime) : undefined;
    const createdAt = new Date(sleeplog.createdAt);

    let quality = sleeplog.quality;
    if (!quality && sleeplog.isCompleted && sleeplog.duration && wakeTime) {
      quality = calculateAutoSleepQuality(sleepTime, wakeTime, sleeplog.duration);
    }

    return {
      id: sleeplog._id,
      title: generateSleepTitle(sleepTime, sleeplog.isCompleted),
      sleepTime: sleepTime,
      wakeTime: wakeTime,
      isCompleted: sleeplog.isCompleted,
      notes: sleeplog.notes,
      duration: sleeplog.duration,
      quality: quality,
      autoQuality: sleeplog.isCompleted && sleeplog.duration && wakeTime
        ? calculateAutoSleepQuality(sleepTime, wakeTime, sleeplog.duration)
        : undefined,
      createdAt: createdAt,
    };
  });
};
