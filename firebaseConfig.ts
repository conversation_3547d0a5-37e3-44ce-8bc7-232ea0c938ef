import { initializeApp, getApps, getApp } from "firebase/app";
import {
  initializeAuth,
  getReactNativePersistence,
  onAuthStateChanged,
  Auth,
} from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import AsyncStorage from "@react-native-async-storage/async-storage";


const firebaseConfig = {
  apiKey: "AIzaSyB5S50vdpuNkM9nOyJP4BI26PVUH9kXWcw",
  authDomain: "pocketmini50028.firebaseapp.com",
  projectId: "pocketmini50028",
  storageBucket: "pocketmini50028.firebasestorage.app",
  messagingSenderId: "1047465247617",
  appId: "1:1047465247617:web:a123651acc3a8395f89e80",
};

const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();

let auth: Auth;
try {
  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage),
  });
} catch (e) {
  const { getAuth } = require("firebase/auth");
  auth = getAuth(app);
}

export const db = getFirestore(app);
export const storage = getStorage(app);
export { auth };

export const listenForAuthChanges = (callback: any) => {
  return onAuthStateChanged(auth, (user) => {
    callback(user);
  });
};